#!/usr/bin/env python3
"""
Script to convert SPRODETA M&E User Manual from Markdown to Word document
"""

import re
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE

def create_word_document():
    """Create and format the Word document from the Markdown content"""

    # Read the markdown file
    try:
        with open('SPRODETA_ME_User_Manual.md', 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print("Error: SPRODETA_ME_User_Manual.md file not found!")
        return

    # Create new document
    doc = Document()

    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)

    # Add header
    header = sections[0].header
    header_para = header.paragraphs[0]
    header_para.text = "SPRODETA M&E System User Manual"
    header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # Add footer with page numbers
    footer = sections[0].footer
    footer_para = footer.paragraphs[0]
    footer_para.text = "© 2025 SPRODETA. All rights reserved."
    footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Process content line by line
    lines = content.split('\n')
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        # Main title
        if line.startswith('# ') and 'SPRODETA' in line:
            title = line[2:].strip()
            p = doc.add_paragraph()
            run = p.add_run(title)
            run.font.name = 'Arial'
            run.font.size = Pt(24)
            run.bold = True
            p.alignment = WD_ALIGN_PARAGRAPH.CENTER
            p.space_after = Pt(12)
            
        # Version information
        elif line.startswith('**Version:**') or line.startswith('**Last Updated:**') or line.startswith('**System Version:**'):
            subtitle = line.replace('**', '').strip()
            p = doc.add_paragraph()
            run = p.add_run(subtitle)
            run.font.name = 'Arial'
            run.font.size = Pt(12)
            run.bold = True
            p.alignment = WD_ALIGN_PARAGRAPH.CENTER
            p.space_after = Pt(6)
            
        # Skip horizontal rules
        elif line.startswith('---'):
            doc.add_paragraph()
            
        # Heading 1 (##)
        elif line.startswith('## '):
            heading = line[3:].strip()
            p = doc.add_paragraph()
            run = p.add_run(heading)
            run.font.name = 'Arial'
            run.font.size = Pt(18)
            run.bold = True
            p.space_before = Pt(18)
            p.space_after = Pt(12)
            
        # Heading 2 (###)
        elif line.startswith('### '):
            heading = line[4:].strip()
            p = doc.add_paragraph()
            run = p.add_run(heading)
            run.font.name = 'Arial'
            run.font.size = Pt(16)
            run.bold = True
            p.space_before = Pt(12)
            p.space_after = Pt(6)
            
        # Heading 3 (####)
        elif line.startswith('#### '):
            heading = line[5:].strip()
            p = doc.add_paragraph()
            run = p.add_run(heading)
            run.font.name = 'Arial'
            run.font.size = Pt(14)
            run.bold = True
            p.space_before = Pt(6)
            p.space_after = Pt(6)
            
        # Bullet points
        elif line.startswith('- ') or line.startswith('* '):
            text = line[2:].strip()
            p = doc.add_paragraph()
            p.style = 'List Bullet'
            
            # Handle bold text in bullet points
            if '**' in text:
                parts = re.split(r'(\*\*.*?\*\*)', text)
                for part in parts:
                    if part.startswith('**') and part.endswith('**'):
                        run = p.add_run(part[2:-2])
                        run.bold = True
                    else:
                        p.add_run(part)
            else:
                p.add_run(text)
            
            p.paragraph_format.left_indent = Inches(0.25)
            
        # Numbered lists
        elif re.match(r'^\d+\. ', line):
            text = re.sub(r'^\d+\. ', '', line).strip()
            p = doc.add_paragraph()
            p.style = 'List Number'
            
            # Handle bold text in numbered lists
            if '**' in text:
                parts = re.split(r'(\*\*.*?\*\*)', text)
                for part in parts:
                    if part.startswith('**') and part.endswith('**'):
                        run = p.add_run(part[2:-2])
                        run.bold = True
                    else:
                        p.add_run(part)
            else:
                p.add_run(text)
                
        # Regular paragraphs
        else:
            p = doc.add_paragraph()
            
            # Handle bold text and other formatting
            if '**' in line:
                parts = re.split(r'(\*\*.*?\*\*)', line)
                for part in parts:
                    if part.startswith('**') and part.endswith('**'):
                        run = p.add_run(part[2:-2])
                        run.bold = True
                    else:
                        p.add_run(part)
            else:
                p.add_run(line)
            
            # Set font for regular text
            for run in p.runs:
                if not run.bold:
                    run.font.name = 'Arial'
                    run.font.size = Pt(11)
    
    # Save the document
    output_filename = 'SPRODETA_ME_User_Manual.docx'
    doc.save(output_filename)
    
    print(f"Successfully created Word document: {output_filename}")
    print("The document has been formatted with:")
    print("- Arial font throughout")
    print("- Proper heading hierarchy")
    print("- Bullet points and numbered lists")
    print("- Bold text formatting")
    print("- Professional spacing and margins")

if __name__ == "__main__":
    create_word_document()
